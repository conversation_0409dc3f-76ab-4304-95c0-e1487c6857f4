# What is Live777 ?

A very simple, high performance, edge WebRTC SFU(**Selective Forwarding Unit**) Server

Live777 is an SFU server for real-time video streaming for the `WHIP`/`WHEP` as first protocol.

## What is SFU Server ?

![webrtc-mesh-mcu-sfu](/webrtc-mesh-mcu-sfu.excalidraw.svg)

## What is `WHIP`/`WHEP` Protocol ?

Live777 supports the conversion of audio and video protocols widely used in the Internet, such as RTP to WHIP or WHEP and other protocols.

![live777-arch](/live777-arch.excalidraw.svg)

Live777 media server is used with [Gstreamer](https://gstreamer.freedesktop.org/), [FFmpeg](https://ffmpeg.org/), [OBS Studio](https://obsproject.com/), [VLC](https://www.videolan.org/), [WebRTC](https://webrtc.org/) and other clients to provide the ability to receive and distribute streams, and is a typical publishing (pushing) and subscription (playing) server model.


