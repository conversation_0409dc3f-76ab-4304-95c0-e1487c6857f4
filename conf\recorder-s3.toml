# Live777 Configuration with AWS S3 Recorder
# This configuration shows how to set up the recorder to store recordings on AWS S3

[http]
# Http Server Listen Address
listen = "[::]:7777"
# Cross-Origin Resource Sharing (CORS)
cors = false

[[ice_servers]]
urls = [
    "stun:stun.22333.fun",
    "stun:cn.22333.fun", 
    "stun:stun.l.google.com:19302",
]

# Recorder configuration for AWS S3 storage
[recorder]
# Automatically record these streams (supports glob patterns)
auto_streams = ["stream-*", "important-*"]

# S3 Storage configuration
[recorder.storage]
type = "s3"
root = "/recordings"                    # Optional: prefix path in bucket
bucket = "my-live777-recordings"        # S3 bucket name
region = "us-east-1"                   # AWS region
endpoint = "https://s3.amazonaws.com"   # Optional: S3 endpoint (use default if not specified)
access_key_id = "YOUR_ACCESS_KEY_ID"    # AWS access key ID
secret_access_key = "YOUR_SECRET_KEY"   # AWS secret access key
enable_virtual_host_style = false      # Use path-style URLs (default: false)

# Environment variables can also be used instead of hardcoding credentials:
# Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables
# and omit access_key_id and secret_access_key from config

[log]
# Log level
level = "info"

[strategy]
# Maximum subscribers per stream
each_stream_max_sub = 100
# Auto delete stream when no publisher (milliseconds, -1 to disable)
auto_delete_whip = 60000
# Auto delete stream when no subscriber (milliseconds, -1 to disable)
auto_delete_whep = 60000
