[http]
# Http Server Listen Address
# listen = "[::]:8888"
# Cross-Origin Resource Sharing (CORS)
# reference: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
# cors = false
# Cascade need proxy all request, each node can connect this address
# public = "http://localhost:8888"

# WHIP/WHEP auth token
# Headers["Authorization"] = "Bearer {token}"
# [auth]
# JSON WEB TOKEN secret
# secret = "<jwt_secret>"
# static JWT token, superadmin, debuggger can use this token
# tokens = ["live777"]

# Admin Dashboard Accounts
# [[auth.accounts]]
# username = "live777"
# password = "live777"

[log]
# Env: `LOG_LEVEL`
# Default: info
# Values: off, error, warn, info, debug, trace
# level = "warn"

[cascade]
# When a new whep beginning, and trigger cascade, check cascade status done times
# Every times check after wait 100ms. If Mare than 5 * 500ms return error
# check_attempts = 5
# Check cluster all node cascade status time interval
# Default: 60s
# check_tick_time = 60000
# If cascade is working, but no consumer subscription, This cascade status is: `idle`
# When status `idle` more than this time duration, close this cascade.
# Default: 60s
# maximum_idle_time = 60000
# When cascade is working, close src server not cascade push session subscription
# close_other_sub = false
# Cascade operating mode
# Options: "push" or "pull". Determines whether cascade operates in push mode or pull mode.
# Default is "push"
# mode = "pull"

# [net4mqtt]
# Global unique alias
# alias = "liveman-0"
# `client_id={alias}` use alias as MQTT `client_id`
# mqtt_url = "mqtt://localhost:1883/net4mqtt?client_id={alias}"
# listen = "127.0.0.1:1077"
# domain = "net4mqtt.local"

# [[nodes]]
# Globally unique id
# alias = "static-0"
# Auth token
# token = "live777"
# Live777 Address
# url = "http://127.0.0.1:7777"

# [[nodes]]
# alias = "static-1"
# token = "live777"
# url = "http://127.0.0.1:7778"

# [[nodes]]
# Globally unique id
# alias = "static-0"
# Auth token
# token = "live777"
# Live777 Address
# url = "http://liveion-0.net4mqtt.local:7777"

