[package]
name = "liveman"
description = "Live777 cluster manager controller"
version.workspace = true
edition.workspace = true
license.workspace = true
repository.workspace = true

[lib]
crate-type = ["lib"]

[dependencies]
net4mqtt = { path = "../libs/net4mqtt", optional = true }

api = { path = "../libs/api" }
auth = { path = "../libs/auth" }
http-log = { path = "../libs/http-log" }
signal = { path = "../libs/signal" }

axum = { workspace = true, features = ["multipart", "tracing"] }
axum-extra = { workspace = true, features = ["typed-header"] }
rust-embed = { workspace = true, features = ["axum-ex"], optional = true }
anyhow = { workspace = true, features = ["backtrace"] }
clap = { workspace = true, features = ["derive"] }
http = { workspace = true }
http-body = { workspace = true }
serde = { workspace = true, features = ["serde_derive"] }
tokio = { workspace = true, features = ["full"] }
tower-http = { workspace = true, features = ["trace", "cors"] }
tracing = { workspace = true }

chrono = { version = "0.4", features = ["serde"] }
http-body-util = "0.1.2"
mime_guess = "2.0.4"
reqwest = { version = "0.12", features = [
    "rustls-tls",
    "socks",
], default-features = false }
serde_json = "1.0.114"
url = "2.5"

[features]
webui = ["dep:rust-embed"]
net4mqtt = ["dep:net4mqtt"]

