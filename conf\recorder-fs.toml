# Live777 Configuration with Local Filesystem Recorder
# This configuration shows how to set up the recorder to store recordings on local filesystem

[http]
# Http Server Listen Address
listen = "[::]:7777"
# Cross-Origin Resource Sharing (CORS)
cors = false

[[ice_servers]]
urls = [
    "stun:stun.22333.fun",
    "stun:cn.22333.fun",
    "stun:stun.l.google.com:19302",
]

# Recorder configuration for local filesystem storage
[recorder]
# Automatically record these streams (supports glob patterns)
auto_streams = ["stream-*", "important-*"]

# Storage configuration
[recorder.storage]
type = "fs"
root = "/var/recordings"  # Directory where recordings will be stored

# Alternative: Use the legacy root field (for backward compatibility)
# root = "file:///var/recordings"

[log]
# Log level
level = "info"

[strategy]
# Maximum subscribers per stream
each_stream_max_sub = 100
# Auto delete stream when no publisher (milliseconds, -1 to disable)
auto_delete_whip = 60000
# Auto delete stream when no subscriber (milliseconds, -1 to disable)  
auto_delete_whep = 60000
