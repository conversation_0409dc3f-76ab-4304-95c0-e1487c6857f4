# Live777 Configuration with Alibaba Cloud OSS Recorder
# This configuration shows how to set up the recorder to store recordings on Alibaba Cloud OSS
# OSS is configured using S3-compatible interface

[http]
# Http Server Listen Address
listen = "[::]:7777"
# Cross-Origin Resource Sharing (CORS)
cors = false

[[ice_servers]]
urls = [
    "stun:stun.22333.fun",
    "stun:cn.22333.fun",
    "stun:stun.l.google.com:19302",
]

# Recorder configuration for Alibaba Cloud OSS storage
[recorder]
# Automatically record these streams (supports glob patterns)
auto_streams = ["stream-*", "important-*"]

# OSS Storage configuration (using S3-compatible interface)
[recorder.storage]
type = "s3"
root = "/recordings"                              # Optional: prefix path in bucket
bucket = "my-live777-recordings"                  # OSS bucket name
region = "oss-cn-hangzhou"                       # OSS region (e.g., oss-cn-hangzhou, oss-cn-beijing)
endpoint = "https://oss-cn-hangzhou.aliyuncs.com" # OSS endpoint for your region
access_key_id = "YOUR_ACCESS_KEY_ID"              # Alibaba Cloud AccessKey ID
secret_access_key = "YOUR_ACCESS_KEY_SECRET"      # Alibaba Cloud AccessKey Secret
enable_virtual_host_style = true                 # Required for OSS compatibility

# Common OSS regions and endpoints:
# - Hangzhou: https://oss-cn-hangzhou.aliyuncs.com
# - Beijing: https://oss-cn-beijing.aliyuncs.com
# - Shanghai: https://oss-cn-shanghai.aliyuncs.com
# - Shenzhen: https://oss-cn-shenzhen.aliyuncs.com
# - Hong Kong: https://oss-cn-hongkong.aliyuncs.com

[log]
# Log level
level = "info"

[strategy]
# Maximum subscribers per stream
each_stream_max_sub = 100
# Auto delete stream when no publisher (milliseconds, -1 to disable)
auto_delete_whip = 60000
# Auto delete stream when no subscriber (milliseconds, -1 to disable)
auto_delete_whep = 60000
