# Recorder

Live777 的 Recorder 模块提供了强大的实时流录制功能，支持将 WebRTC 流录制为 MPEG-DASH 格式并存储到多种存储后端。这是一个可选feature，需要在编译时启用 `recorder` feature.

## 配置说明 

### 基础配置

```toml  
[recorder]
# 自动录制的流名称模式（支持通配符）
auto_streams = ["stream-*", "important-*"]
```

### 存储后端配置

#### 本地文件系统

```toml
[recorder.storage]
type = "fs"
root = "/var/recordings"  # 录制文件存储目录
```

#### AWS S3

```toml
[recorder.storage]
type = "s3"
root = "/recordings"                   # 可选：存储桶内的前缀路径
bucket = "my-live777-recordings"       # S3 存储桶名称
region = "us-east-1"                   # AWS 区域
endpoint = "https://s3.amazonaws.com"  # 可选：S3 端点
access_key_id = "YOUR_ACCESS_KEY_ID"   # AWS 访问密钥 ID
secret_access_key = "YOUR_SECRET_KEY"  # AWS 密钥
enable_virtual_host_style = false      # 使用路径风格 URL
```

#### 阿里云 OSS

```toml
[recorder.storage]
type = "s3"                                       # 使用 S3 兼容接口
root = "/recordings"                              # 可选：存储桶内的前缀路径
bucket = "my-live777-recordings"                  # OSS 存储桶名称
region = "oss-cn-hangzhou"                       # OSS 区域
endpoint = "https://oss-cn-hangzhou.aliyuncs.com" # OSS 端点
access_key_id = "YOUR_ACCESS_KEY_ID"              # 阿里云 AccessKey ID
secret_access_key = "YOUR_ACCESS_KEY_SECRET"      # 阿里云 AccessKey Secret
enable_virtual_host_style = true                 # OSS 需要启用虚拟主机风格
```

## 使用方法

### 自动录制

配置 `auto_streams` 字段，匹配的流将自动开始录制：

```toml
[recorder]
auto_streams = [
    "meeting-*",      # 录制所有以 meeting- 开头的流
    "webinar-*",      # 录制所有以 webinar- 开头的流
    "important-stream" # 录制特定流
]
```

### 手动录制

通过 API 手动开始/停止录制：

```bash
# 开始录制
curl -X POST http://localhost:7777/api/recorder/start/stream-name

# 停止录制
curl -X POST http://localhost:7777/api/recorder/stop/stream-name

# 查看录制状态
curl http://localhost:7777/api/recorder/status/stream-name
```

## 文件组织结构

录制文件按以下结构组织：

```
recordings/
├── stream-name/
│   ├── 2024/
│   │   ├── 01/
│   │   │   ├── 15/
│   │   │   │   ├── video_001.mp4
│   │   │   │   ├── video_002.mp4
│   │   │   │   ├── audio_001.mp4
│   │   │   │   └── audio_002.mp4
│   │   │   └── ...
│   │   └── ...
│   └── ...
└── ...
```

- 按流名称分组
- 按年/月/日分层组织
- 视频和音频分别存储
- 每个片段默认 10 秒

## 环境变量

支持通过环境变量配置敏感信息：

```bash
# AWS S3
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_REGION="us-east-1"

# 阿里云 OSS
export ALIBABA_CLOUD_ACCESS_KEY_ID="your-access-key"
export ALIBABA_CLOUD_ACCESS_KEY_SECRET="your-secret-key"
```

## 性能优化

### 存储性能
- 使用 SSD 存储以获得更好的写入性能
- 对于云存储，选择就近的区域以减少延迟
- 配置适当的网络带宽以支持并发录制

### 系统资源
- 每个录制任务约占用 50-100MB 内存
- CPU 使用率主要取决于并发录制流的数量
- 磁盘 I/O 与录制质量和并发数成正比

## 故障排除

### 常见问题

1. **录制文件不完整**
   - 检查存储空间是否充足
   - 确认网络连接稳定（云存储）
   - 查看日志中的错误信息

2. **无法连接到云存储**
   - 验证访问密钥和权限
   - 检查网络连接和防火墙设置
   - 确认存储桶名称和区域配置正确

3. **录制质量问题**
   - 检查上游是否提供关键帧
   - 调整关键帧请求超时设置
   - 监控网络丢包情况

### 日志分析

启用详细日志以诊断问题：

```toml
[log]
level = "debug"
```

关键日志标识：
- `[recorder]`: 录制器相关日志
- `[segmenter]`: 分段器相关日志
- `[storage]`: 存储相关日志

## 配置样例

项目提供了完整的配置样例文件：

- `conf/recorder-fs.toml`: 本地文件系统配置
- `conf/recorder-s3.toml`: AWS S3 配置
- `conf/recorder-oss.toml`: 阿里云 OSS 配置

可以基于这些样例快速配置您的录制环境。
