# Web UI

This Web UI is build in live777

## Web WHIP/WHEP client

**Open your browser, enter the URL: `http://localhost:7777/`**

```
http://localhost:7777/
```

## Debugger

example:

```
http://localhost:7777/tools/debugger.html
```

You can use this test WebRTC-SVC

## Single Page Player

example:

```
http://localhost:7777/tools/player.html?id=web-0&autoplay&controls&muted&reconnect=3000
```

URL params:

- `id`: string, live777 Stream ID
- `autoplay`: boolean
- `controls`: boolean
- `muted`: boolean, whether to mute by default
- `reconnect`: number, reconnect timeout in millisecond

