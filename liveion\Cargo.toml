[package]
name = "liveion"
description = "A very simple, high performance, edge WebRTC SFU"
version.workspace = true
edition.workspace = true
license.workspace = true
repository.workspace = true

[lib]
crate-type = ["lib"]

[dependencies]
api = { path = "../libs/api" }
auth = { path = "../libs/auth" }
http-log = { path = "../libs/http-log" }
libwish = { path = "../libs/libwish" }

net4mqtt = { path = "../libs/net4mqtt", optional = true }

axum = { workspace = true, features = ["multipart", "tracing"] }
axum-extra = { workspace = true, features = ["query"] }
tower-http = { workspace = true, features = ["trace", "cors"] }
rust-embed = { workspace = true, features = ["axum-ex"], optional = true }
anyhow = { workspace = true, features = ["backtrace"] }
clap = { workspace = true, features = ["derive"] }
http = { workspace = true }
http-body = { workspace = true }
serde = { workspace = true, features = ["serde_derive"] }
tokio = { workspace = true, features = ["full"] }
tokio-stream = "0.1.15"
async-stream = "0.3.5"
tracing = { workspace = true }
webrtc = { workspace = true }

async-trait = "0.1"
chrono = "0.4"
hyper = "1.2.0"
lazy_static = "1.4.0"
md5 = "0.8.0"
mime_guess = "2.0.4"
prometheus = "0.14"
serde_json = "1.0.114"
reqwest = { version = "0.12", features = [
    "rustls-tls",
], default-features = false }


opendal = { version = "0.53.3", features = ["services-fs", "services-s3"], optional = true }
once_cell = "1.19"
bytes = { version = "1.6.0", optional = true }
byteorder = { version = "1.5", optional = true }
livetwo = { path = "../livetwo", optional = true }
h264-reader = { version = "0.8", optional = true }

glob = "0.3"

[features]
webui = ["dep:rust-embed"]
net4mqtt = ["dep:net4mqtt"]
recorder = [
    "dep:opendal", "dep:bytes", "dep:h264-reader", "dep:livetwo", "dep:byteorder"
]

[dev-dependencies]
tempfile = "3.10"
toml = "0.8"

