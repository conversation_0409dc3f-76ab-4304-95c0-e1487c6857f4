use std::{env, net::SocketAddr, str::FromStr};

use serde::{Deserialize, Serialize};
use webrtc::{
    ice,
    ice_transport::{ice_credential_type::RTCIceCredentialType, ice_server::RTCIceServer},
    Error,
};

#[derive(Debu<PERSON>, <PERSON><PERSON>ult, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct Config {
    #[serde(default)]
    pub http: Http,
    #[serde(default = "default_ice_servers")]
    pub ice_servers: Vec<IceServer>,
    #[serde(default)]
    pub auth: Auth,
    #[serde(default)]
    pub log: Log,
    #[serde(default)]
    pub strategy: api::strategy::Strategy,

    #[cfg(feature = "net4mqtt")]
    #[serde(default)]
    pub net4mqtt: Option<Net4mqtt>,

    #[serde(default)]
    pub webhook: Webhook,

    #[cfg(feature = "recorder")]
    #[serde(default)]
    pub recorder: RecorderConfig,
}

#[cfg(feature = "net4mqtt")]
#[derive(Debug, <PERSON><PERSON>ult, <PERSON><PERSON>, Deserialize, Ser<PERSON>ize)]
pub struct Net4mqtt {
    #[serde(default)]
    pub mqtt_url: String,
    #[serde(default)]
    pub alias: String,
}

#[cfg(feature = "net4mqtt")]
impl Net4mqtt {
    pub fn validate(&mut self) {
        self.mqtt_url = self.mqtt_url.replace("{alias}", &self.alias)
    }
}

#[derive(Debug, Default, Clone, Deserialize, Serialize)]
pub struct Webhook {
    #[serde(default)]
    pub webhooks: Vec<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct Http {
    #[serde(default = "default_http_listen")]
    pub listen: SocketAddr,
    #[serde(default)]
    pub cors: bool,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct Auth {
    #[serde(default)]
    pub secret: String,
    #[serde(default)]
    pub tokens: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Log {
    #[serde(default = "default_log_level")]
    pub level: String,
}

fn default_http_listen() -> SocketAddr {
    SocketAddr::from_str(&format!(
        "0.0.0.0:{}",
        env::var("PORT").unwrap_or(String::from("7777"))
    ))
    .expect("invalid listen address")
}

impl Default for Http {
    fn default() -> Self {
        Self {
            listen: default_http_listen(),
            cors: Default::default(),
        }
    }
}

fn default_ice_servers() -> Vec<IceServer> {
    vec![IceServer {
        urls: vec!["stun:stun.l.google.com:19302".to_string()],
        username: "".to_string(),
        credential: "".to_string(),
        credential_type: "".to_string(),
    }]
}

impl Default for Log {
    fn default() -> Self {
        Self {
            level: default_log_level(),
        }
    }
}

fn default_log_level() -> String {
    env::var("LOG_LEVEL").unwrap_or_else(|_| {
        if cfg!(debug_assertions) {
            "debug".to_string()
        } else {
            "info".to_string()
        }
    })
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct IceServer {
    #[serde(default)]
    pub urls: Vec<String>,
    #[serde(default)]
    pub username: String,
    #[serde(default)]
    pub credential: String,
    #[serde(default)]
    pub credential_type: String,
}

// from https://github.com/webrtc-rs/webrtc/blob/71157ba2153a891a8cfd819f3cf1441a7a0808d8/webrtc/src/ice_transport/ice_server.rs
impl IceServer {
    pub(crate) fn parse_url(&self, url_str: &str) -> webrtc::error::Result<ice::url::Url> {
        Ok(ice::url::Url::parse_url(url_str)?)
    }

    pub(crate) fn validate(&self) -> webrtc::error::Result<()> {
        self.urls()?;
        Ok(())
    }

    pub(crate) fn urls(&self) -> webrtc::error::Result<Vec<ice::url::Url>> {
        let mut urls = vec![];

        for url_str in &self.urls {
            let mut url = self.parse_url(url_str)?;
            if url.scheme == ice::url::SchemeType::Turn || url.scheme == ice::url::SchemeType::Turns
            {
                // https://www.w3.org/TR/webrtc/#set-the-configuration (step #11.3.2)
                if self.username.is_empty() || self.credential.is_empty() {
                    return Err(Error::ErrNoTurnCredentials);
                }
                url.username.clone_from(&self.username);

                match self.credential_type.as_str().into() {
                    RTCIceCredentialType::Password => {
                        // https://www.w3.org/TR/webrtc/#set-the-configuration (step #11.3.3)
                        url.password.clone_from(&self.credential);
                    }
                    RTCIceCredentialType::Oauth => {
                        // https://www.w3.org/TR/webrtc/#set-the-configuration (step #11.3.4)
                        /*if _, ok: = s.Credential.(OAuthCredential); !ok {
                                return nil,
                                &rtcerr.InvalidAccessError{Err: ErrTurnCredentials
                            }
                        }*/
                    }
                    _ => return Err(Error::ErrTurnCredentials),
                };
            }

            urls.push(url);
        }

        Ok(urls)
    }
}

impl From<IceServer> for RTCIceServer {
    fn from(val: IceServer) -> Self {
        RTCIceServer {
            urls: val.urls,
            username: val.username,
            credential: val.credential,
            credential_type: val.credential_type.as_str().into(),
        }
    }
}

impl Config {
    pub fn validate(&self) -> anyhow::Result<()> {
        for ice_server in self.ice_servers.iter() {
            ice_server
                .validate()
                .map_err(|e| anyhow::anyhow!(format!("ice_server error : {}", e)))?;
        }
        Ok(())
    }
}

#[cfg(feature = "recorder")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecorderConfig {
    /// List of stream names to automatically record
    #[serde(default)]
    pub auto_streams: Vec<String>,

    /// OpenDAL Scheme (for backward compatibility)
    #[serde(default = "default_recorder_root")]
    pub root: String,

    /// Storage configuration (overrides root if present)
    pub storage: Option<StorageConfig>,
}

#[cfg(feature = "recorder")]
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum StorageConfig {
    #[serde(rename = "fs")]
    Fs {
        #[serde(default = "default_recorder_root")]
        root: String,
    },
    #[serde(rename = "s3")]
    S3 {
        root: Option<String>,
        bucket: String,
        region: Option<String>,
        endpoint: Option<String>,
        access_key_id: Option<String>,
        secret_access_key: Option<String>,
        #[serde(default)]
        enable_virtual_host_style: bool,
    },
}

#[cfg(feature = "recorder")]
impl Default for RecorderConfig {
    fn default() -> Self {
        Self {
            auto_streams: Vec::new(),
            root: default_recorder_root(),
            storage: None,
        }
    }
}

#[cfg(feature = "recorder")]
fn default_recorder_root() -> String {
    "file://./records".to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[cfg(feature = "recorder")]
    #[test]
    fn test_recorder_config_parsing() {
        // Test backward compatibility with root field
        let toml_str = r#"
            auto_streams = ["stream-*"]
            root = "file://./test-records"
        "#;
        let config: RecorderConfig = toml::from_str(toml_str).unwrap();
        assert_eq!(config.root, "file://./test-records");
        assert_eq!(config.auto_streams, vec!["stream-*"]);
        assert!(config.storage.is_none());

        // Test new storage configuration - filesystem
        let toml_str = r#"
            auto_streams = ["stream-*"]

            [storage]
            type = "fs"
            root = "/var/recordings"
        "#;
        let config: RecorderConfig = toml::from_str(toml_str).unwrap();
        assert!(config.storage.is_some());
        if let Some(StorageConfig::Fs { root }) = &config.storage {
            assert_eq!(root, "/var/recordings");
        } else {
            panic!("Expected Fs storage config");
        }

        // Test new storage configuration - S3
        let toml_str = r#"
            auto_streams = ["stream-*"]

            [storage]
            type = "s3"
            bucket = "test-bucket"
            region = "us-east-1"
            endpoint = "https://s3.amazonaws.com"
            access_key_id = "test-key"
            secret_access_key = "test-secret"
            enable_virtual_host_style = true
        "#;
        let config: RecorderConfig = toml::from_str(toml_str).unwrap();
        assert!(config.storage.is_some());
        if let Some(StorageConfig::S3 {
            bucket,
            region,
            endpoint,
            access_key_id,
            secret_access_key,
            enable_virtual_host_style,
            ..
        }) = &config.storage {
            assert_eq!(bucket, "test-bucket");
            assert_eq!(region.as_ref().unwrap(), "us-east-1");
            assert_eq!(endpoint.as_ref().unwrap(), "https://s3.amazonaws.com");
            assert_eq!(access_key_id.as_ref().unwrap(), "test-key");
            assert_eq!(secret_access_key.as_ref().unwrap(), "test-secret");
            assert_eq!(enable_virtual_host_style, &true);
        } else {
            panic!("Expected S3 storage config");
        }
    }
}
